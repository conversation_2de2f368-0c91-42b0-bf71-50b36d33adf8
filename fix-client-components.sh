#!/bin/bash

echo "🔧 Adding 'use client' directive to pages that use MainLayout..."

# List of pages that use MainLayout
pages=(
  "src/app/conflicts/page.tsx"
  "src/app/end-users/page.tsx"
  "src/app/admin/users/page.tsx"
  "src/app/profile/page.tsx"
  "src/app/deals/new/page.tsx"
  "src/app/deals/page.tsx"
  "src/app/settings/page.tsx"
)

for page in "${pages[@]}"; do
  if [ -f "$page" ]; then
    # Check if it already has "use client"
    if ! grep -q "^[\"']use client[\"']" "$page"; then
      echo "Adding 'use client' to $page"
      # Add "use client" at the beginning of the file
      sed -i '1i"use client"\n' "$page"
    else
      echo "$page already has 'use client'"
    fi
  else
    echo "Warning: $page not found"
  fi
done

echo "✅ All pages updated with 'use client' directive!"
